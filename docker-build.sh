#!/bin/bash

# TrustAI Docker Build Script

echo "🛡️ TrustAI Docker Build Script"
echo "=============================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

echo "📦 Building TrustAI Docker images with ML support..."

# Prepare ML models directory
echo "🤖 Preparing ML models..."
if [ ! -d "models" ]; then
    echo "📁 Creating models directory..."
    mkdir -p models
fi

# Optional: Pre-train models locally if Python is available
if command -v python3 &> /dev/null; then
    echo "📊 Pre-generating training data..."
    python3 train_models.py --action generate-data --samples 1000 2>/dev/null || echo "⚠️ Local training data generation skipped"

    echo "🎓 Pre-training ML models..."
    python3 train_models.py --action train 2>/dev/null || echo "⚠️ Local model training skipped"
else
    echo "⚠️ Python not found locally, ML models will be trained in container"
fi

# Build backend image with ML support
echo "🔧 Building backend image with ML support..."
docker build -f Dockerfile.backend -t trustai-backend:latest .

if [ $? -eq 0 ]; then
    echo "✅ Backend image built successfully"
else
    echo "❌ Failed to build backend image"
    exit 1
fi

# Build frontend image
echo "🎨 Building frontend image..."
cd frontend
docker build -f Dockerfile -t trustai-frontend:latest .

if [ $? -eq 0 ]; then
    echo "✅ Frontend image built successfully"
else
    echo "❌ Failed to build frontend image"
    exit 1
fi

cd ..

echo "🎉 All Docker images built successfully with ML support!"
echo ""
echo "🚀 To start TrustAI with Docker:"
echo "   docker-compose up -d"
echo ""
echo "🌐 Access points:"
echo "   Frontend: http://localhost:3000"
echo "   Backend:  http://localhost:5000"
echo "   Nginx:    http://localhost:80 (production profile)"
echo ""
echo "🤖 ML Features:"
echo "   • Advanced fraud detection with 58+ features"
echo "   • Ensemble ML models (Random Forest, XGBoost, LightGBM)"
echo "   • Real-time anomaly detection"
echo "   • Hybrid ML + rule-based predictions"
