"""
TrustAI ML-Enhanced Trust Engine
Combines rule-based logic with advanced machine learning models
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import json

from .trust_engine import TrustEngine
from .ml_feature_engineering import FeatureEngineer
from .ml_models import MLFraudDetector

logger = logging.getLogger(__name__)

class MLTrustEngine(TrustEngine):
    """
    Enhanced trust engine with ML capabilities
    """
    
    def __init__(self, database):
        super().__init__(database)
        
        # Initialize ML components
        self.feature_engineer = FeatureEngineer(database)
        self.ml_detector = MLFraudDetector(database, self.feature_engineer)
        
        # Configuration
        self.use_ml = True  # Flag to enable/disable ML
        self.ml_weight = 0.7  # Weight for ML prediction vs rule-based
        self.rule_weight = 0.3  # Weight for rule-based prediction
        
        # Performance tracking
        self.prediction_cache = {}
        self.performance_metrics = {
            'ml_predictions': 0,
            'rule_predictions': 0,
            'hybrid_predictions': 0,
            'cache_hits': 0
        }
    
    def analyze_activity(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced activity analysis using ML + rules
        """
        try:
            user_id = context['user_id']
            
            # Check cache first
            cache_key = self._generate_cache_key(context)
            if cache_key in self.prediction_cache:
                self.performance_metrics['cache_hits'] += 1
                return self.prediction_cache[cache_key]
            
            # Get ML prediction
            ml_result = None
            if self.use_ml:
                try:
                    ml_result = self.ml_detector.predict_fraud_probability(context, user_id)
                    self.performance_metrics['ml_predictions'] += 1
                except Exception as e:
                    logger.warning(f"ML prediction failed, falling back to rules: {str(e)}")
            
            # Get rule-based prediction
            rule_result = super().analyze_activity(context)
            self.performance_metrics['rule_predictions'] += 1
            
            # Combine predictions
            if ml_result and self.use_ml:
                combined_result = self._combine_predictions(ml_result, rule_result, context)
                self.performance_metrics['hybrid_predictions'] += 1
            else:
                combined_result = rule_result
            
            # Enhanced explanation
            combined_result['explanation'] = self._generate_enhanced_explanation(
                ml_result, rule_result, combined_result
            )
            
            # Add ML insights
            if ml_result:
                combined_result['ml_insights'] = {
                    'fraud_probability': ml_result['fraud_probability'],
                    'anomaly_score': ml_result['anomaly_score'],
                    'model_version': ml_result['model_version'],
                    'top_risk_factors': self._get_top_risk_factors(ml_result)
                }
            
            # Cache result
            self.prediction_cache[cache_key] = combined_result
            
            # Store enhanced result
            self._store_enhanced_result(user_id, combined_result, ml_result)
            
            return combined_result
            
        except Exception as e:
            logger.error(f"Enhanced trust analysis error: {str(e)}")
            # Fallback to parent implementation
            return super().analyze_activity(context)
    
    def _combine_predictions(self, ml_result: Dict[str, Any], 
                           rule_result: Dict[str, Any], 
                           context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Combine ML and rule-based predictions intelligently
        """
        # Extract scores
        ml_trust_score = ml_result['trust_score']
        rule_trust_score = rule_result['score']
        
        # Adaptive weighting based on confidence
        ml_confidence = self._calculate_ml_confidence(ml_result)
        rule_confidence = self._calculate_rule_confidence(rule_result)
        
        # Adjust weights based on confidence
        adjusted_ml_weight = self.ml_weight * ml_confidence
        adjusted_rule_weight = self.rule_weight * rule_confidence
        
        # Normalize weights
        total_weight = adjusted_ml_weight + adjusted_rule_weight
        if total_weight > 0:
            adjusted_ml_weight /= total_weight
            adjusted_rule_weight /= total_weight
        else:
            adjusted_ml_weight = 0.5
            adjusted_rule_weight = 0.5
        
        # Combined trust score
        combined_score = (ml_trust_score * adjusted_ml_weight + 
                         rule_trust_score * adjusted_rule_weight)
        
        # Risk level based on combined score
        combined_risk_level = self._determine_risk_level(combined_score)
        
        # Decision logic with ML insights
        decision = self._make_enhanced_decision(
            combined_score, combined_risk_level, ml_result, rule_result, context
        )
        
        # Build combined result
        combined_result = {
            'score': round(combined_score, 2),
            'risk_level': combined_risk_level,
            'decision': decision['action'],
            'requires_mfa': decision.get('requires_mfa', False),
            'requires_verification': decision.get('requires_verification', False),
            'recommended_actions': decision.get('recommended_actions', []),
            'timestamp': datetime.utcnow().isoformat(),
            'prediction_method': 'ml_hybrid',
            'ml_weight': round(adjusted_ml_weight, 3),
            'rule_weight': round(adjusted_rule_weight, 3),
            'component_scores': {
                'ml_score': ml_trust_score,
                'rule_score': rule_trust_score,
                'ml_confidence': ml_confidence,
                'rule_confidence': rule_confidence
            }
        }
        
        # Add risk factors from both methods
        combined_result['risk_factors'] = self._combine_risk_factors(
            ml_result, rule_result
        )
        
        return combined_result
    
    def _calculate_ml_confidence(self, ml_result: Dict[str, Any]) -> float:
        """
        Calculate confidence in ML prediction
        """
        try:
            # Factors that affect ML confidence
            confidence = 1.0
            
            # Model agreement (if multiple models agree, higher confidence)
            predictions = ml_result.get('model_predictions', {})
            if len(predictions) > 1:
                pred_values = list(predictions.values())
                std_dev = np.std(pred_values)
                confidence *= max(0.5, 1.0 - std_dev)  # Lower std = higher confidence
            
            # Anomaly score (high anomaly = lower confidence in normal prediction)
            anomaly_score = ml_result.get('anomaly_score', 0.0)
            if anomaly_score > 0.8:
                confidence *= 0.7
            elif anomaly_score > 0.5:
                confidence *= 0.9
            
            # Model version (newer models might be more confident)
            model_version = ml_result.get('model_version', 'unknown')
            if model_version == 'fallback':
                confidence *= 0.3
            
            return max(0.1, min(1.0, confidence))
            
        except Exception as e:
            logger.error(f"Error calculating ML confidence: {str(e)}")
            return 0.5
    
    def _calculate_rule_confidence(self, rule_result: Dict[str, Any]) -> float:
        """
        Calculate confidence in rule-based prediction
        """
        try:
            # Rule-based confidence based on data availability
            confidence = 1.0
            
            # Check if we have sufficient data for rule-based analysis
            risk_factors = rule_result.get('risk_factors', {})
            
            # Penalize if many factors are at default/neutral values
            neutral_factors = sum(1 for score in risk_factors.values() 
                                if 45 <= score <= 55)  # Neutral range
            
            if len(risk_factors) > 0:
                neutral_ratio = neutral_factors / len(risk_factors)
                confidence *= max(0.3, 1.0 - neutral_ratio)
            
            # Higher confidence for extreme scores (very high or very low)
            score = rule_result.get('score', 50)
            if score > 80 or score < 30:
                confidence *= 1.2  # More confident in extreme predictions
            
            return max(0.1, min(1.0, confidence))
            
        except Exception as e:
            logger.error(f"Error calculating rule confidence: {str(e)}")
            return 0.5
    
    def _make_enhanced_decision(self, trust_score: float, risk_level: str,
                              ml_result: Dict[str, Any], rule_result: Dict[str, Any],
                              context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make enhanced decision using both ML and rule insights
        """
        decision = {
            'action': 'allow',
            'requires_mfa': False,
            'requires_verification': False,
            'recommended_actions': []
        }
        
        # Get ML fraud probability
        fraud_prob = ml_result.get('fraud_probability', 0.0) if ml_result else 0.0
        anomaly_score = ml_result.get('anomaly_score', 0.0) if ml_result else 0.0
        
        # Enhanced decision logic
        if risk_level == 'high' or fraud_prob > 0.8:
            decision['action'] = 'block'
            decision['recommended_actions'] = [
                'Contact customer support for identity verification',
                'Review account security settings',
                'Consider account lockdown if fraud confirmed'
            ]
            
            # Add ML-specific recommendations
            if fraud_prob > 0.9:
                decision['recommended_actions'].append(
                    'High fraud probability detected by ML models'
                )
        
        elif risk_level == 'medium' or fraud_prob > 0.4 or anomaly_score > 0.6:
            if context['action'] == 'login':
                decision['requires_mfa'] = True
                decision['action'] = 'challenge'
            elif context['action'] == 'transaction':
                decision['requires_verification'] = True
                decision['action'] = 'verify'
            
            decision['recommended_actions'] = [
                'Complete additional verification',
                'Review transaction details carefully'
            ]
            
            # Add specific reasons based on ML insights
            if anomaly_score > 0.7:
                decision['recommended_actions'].append(
                    'Unusual behavior pattern detected'
                )
            if fraud_prob > 0.5:
                decision['recommended_actions'].append(
                    'Elevated fraud risk identified by AI models'
                )
        
        # Special handling for high-value transactions
        amount = context.get('amount', 0)
        if amount > 170560 and (fraud_prob > 0.3 or anomaly_score > 0.5):  # ₹1,70,560
            decision['requires_verification'] = True
            if decision['action'] == 'allow':
                decision['action'] = 'verify'
            decision['recommended_actions'].append(
                'High-value transaction requires additional verification'
            )
        
        return decision
    
    def _combine_risk_factors(self, ml_result: Dict[str, Any], 
                            rule_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Combine risk factors from ML and rule-based analysis
        """
        combined_factors = {}
        
        # Start with rule-based factors
        rule_factors = rule_result.get('risk_factors', {})
        for factor, score in rule_factors.items():
            combined_factors[f'rule_{factor}'] = score
        
        # Add ML-derived factors
        if ml_result and 'feature_importance' in ml_result:
            feature_importance = ml_result['feature_importance']
            
            # Convert top important features to risk factors
            for model_name, importance_dict in feature_importance.items():
                for feature, importance in list(importance_dict.items())[:5]:  # Top 5
                    factor_name = f'ml_{model_name}_{feature}'
                    # Convert importance to risk score (higher importance = higher risk)
                    risk_score = min(100, importance * 100)
                    combined_factors[factor_name] = risk_score
        
        return combined_factors
    
    def _generate_enhanced_explanation(self, ml_result: Optional[Dict[str, Any]],
                                     rule_result: Dict[str, Any],
                                     combined_result: Dict[str, Any]) -> str:
        """
        Generate enhanced explanation combining ML and rule insights
        """
        explanations = []
        
        # Base explanation
        trust_score = combined_result['score']
        explanations.append(f"Trust score: {trust_score:.1f}/100")
        
        # ML insights
        if ml_result:
            fraud_prob = ml_result['fraud_probability']
            anomaly_score = ml_result['anomaly_score']
            
            if fraud_prob > 0.7:
                explanations.append("High fraud probability detected by AI models")
            elif fraud_prob > 0.4:
                explanations.append("Moderate fraud risk identified by AI analysis")
            
            if anomaly_score > 0.7:
                explanations.append("Unusual behavior pattern detected")
            elif anomaly_score > 0.4:
                explanations.append("Some behavioral anomalies observed")
        
        # Rule-based insights
        rule_explanation = rule_result.get('explanation', '')
        if 'security checks passed' not in rule_explanation.lower():
            # Extract specific issues from rule explanation
            if 'device' in rule_explanation.lower():
                explanations.append("Device-related security concerns")
            if 'location' in rule_explanation.lower():
                explanations.append("Location-based risk factors")
            if 'velocity' in rule_explanation.lower():
                explanations.append("Transaction velocity concerns")
        
        # Prediction method info
        method = combined_result.get('prediction_method', 'unknown')
        if method == 'ml_hybrid':
            ml_weight = combined_result.get('ml_weight', 0)
            explanations.append(f"Analysis combines AI models ({ml_weight:.0%}) with security rules")
        
        return ". ".join(explanations) + "."
    
    def _get_top_risk_factors(self, ml_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract top risk factors from ML analysis
        """
        top_factors = []
        
        feature_importance = ml_result.get('feature_importance', {})
        
        # Aggregate importance across all models
        aggregated_importance = {}
        for model_name, importance_dict in feature_importance.items():
            for feature, importance in importance_dict.items():
                if feature not in aggregated_importance:
                    aggregated_importance[feature] = 0
                aggregated_importance[feature] += importance
        
        # Get top 5 factors
        sorted_factors = sorted(aggregated_importance.items(), 
                              key=lambda x: x[1], reverse=True)[:5]
        
        for feature, importance in sorted_factors:
            top_factors.append({
                'factor': feature,
                'importance': round(importance, 3),
                'description': self._get_factor_description(feature)
            })
        
        return top_factors
    
    def _get_factor_description(self, feature: str) -> str:
        """
        Get human-readable description for ML features
        """
        descriptions = {
            'transaction_amount': 'Transaction amount',
            'txn_count_24h': 'Transaction frequency (24h)',
            'device_fingerprint_entropy': 'Device uniqueness',
            'geo_new_location': 'New location detected',
            'account_age_days': 'Account age',
            'behavior_pattern_deviation': 'Behavioral pattern change',
            'network_device_shared': 'Shared device usage',
            'hour_of_day': 'Time of activity',
            'is_weekend': 'Weekend activity',
            'txn_velocity_score': 'Transaction velocity'
        }
        
        return descriptions.get(feature, feature.replace('_', ' ').title())
    
    def _generate_cache_key(self, context: Dict[str, Any]) -> str:
        """
        Generate cache key for prediction caching
        """
        # Create a hash of relevant context elements
        key_elements = [
            str(context.get('user_id', '')),
            context.get('action', ''),
            str(context.get('amount', 0)),
            context.get('ip_address', ''),
            context.get('timestamp', datetime.utcnow()).strftime('%Y%m%d%H')  # Hour precision
        ]
        
        return '_'.join(key_elements)
    
    def _store_enhanced_result(self, user_id: int, combined_result: Dict[str, Any],
                             ml_result: Optional[Dict[str, Any]]) -> None:
        """
        Store enhanced analysis result with ML metadata
        """
        # Store the standard result
        super()._store_trust_result(user_id, combined_result)
        
        # Store ML-specific metadata if available
        if ml_result:
            ml_metadata = {
                'fraud_probability': ml_result['fraud_probability'],
                'anomaly_score': ml_result['anomaly_score'],
                'model_version': ml_result['model_version'],
                'model_predictions': ml_result['model_predictions'],
                'prediction_timestamp': ml_result['prediction_timestamp']
            }
            
            # Store in database (would need to extend database schema)
            try:
                self.db.store_ml_metadata(user_id, ml_metadata)
            except AttributeError:
                # Database doesn't support ML metadata yet
                logger.debug("ML metadata storage not implemented in database")
    
    def train_models(self) -> Dict[str, Any]:
        """
        Train ML models using historical data
        """
        return self.ml_detector.train_models()
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about ML models
        """
        return self.ml_detector.get_model_info()
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for the enhanced engine
        """
        return {
            'prediction_metrics': self.performance_metrics,
            'cache_size': len(self.prediction_cache),
            'ml_enabled': self.use_ml,
            'ml_weight': self.ml_weight,
            'rule_weight': self.rule_weight
        }
    
    def toggle_ml(self, enabled: bool) -> None:
        """
        Enable or disable ML predictions
        """
        self.use_ml = enabled
        logger.info(f"ML predictions {'enabled' if enabled else 'disabled'}")
    
    def update_weights(self, ml_weight: float, rule_weight: float) -> None:
        """
        Update prediction weights
        """
        total = ml_weight + rule_weight
        if total > 0:
            self.ml_weight = ml_weight / total
            self.rule_weight = rule_weight / total
            logger.info(f"Updated weights: ML={self.ml_weight:.2f}, Rules={self.rule_weight:.2f}")
    
    def clear_cache(self) -> None:
        """
        Clear prediction cache
        """
        self.prediction_cache.clear()
        logger.info("Prediction cache cleared")
