# TrustAI Backend Dockerfile with ML Support
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies for ML packages
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    gfortran \
    libopenblas-dev \
    liblapack-dev \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies with ML support
# Install numpy first to avoid compilation issues
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir numpy==1.24.3 && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p logs data models

# Initialize database
RUN python init_db.py

# Generate initial training data and train models
RUN python train_models.py --action generate-data --samples 1000 || true
RUN python train_models.py --action train || true

# Create non-root user
RUN useradd --create-home --shell /bin/bash trustai
RUN chown -R trustai:trustai /app
USER trustai

# Set environment variables for ML optimization
ENV OMP_NUM_THREADS=1
ENV OPENBLAS_NUM_THREADS=1
ENV PYTHONPATH=/app

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/api/health || exit 1

# Run the application with ML-enhanced trust engine
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "2", "--threads", "4", "--timeout", "120", "--worker-class", "gthread", "app:app"]
