#!/usr/bin/env python3
"""
TrustAI Model Training Script
Train and evaluate ML models for fraud detection
"""

import os
import sys
import logging
import argparse
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from src.database import Database
from src.ml_feature_engineering import FeatureEngineer
from src.ml_models import MLFraudDetector
from src.demo_data import DemoDataGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('model_training.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def generate_training_data(db: Database, num_samples: int = 5000) -> None:
    """
    Generate synthetic training data for model training
    """
    logger.info(f"Generating {num_samples} training samples...")
    
    demo_generator = DemoDataGenerator(db)
    
    # Generate diverse user activities with labels
    for i in range(num_samples):
        if i % 1000 == 0:
            logger.info(f"Generated {i}/{num_samples} samples")
        
        # Create random user activity
        user_id = demo_generator.fake.random_int(min=1, max=100)
        
        # Generate activity context
        activity_type = demo_generator.fake.random_element([
            'login', 'transaction', 'profile_update', 'password_change'
        ])
        
        timestamp = demo_generator.fake.date_time_between(
            start_date='-30d', end_date='now'
        )
        
        # Generate transaction details if needed
        amount = 0
        merchant = ''
        if activity_type == 'transaction':
            amount = demo_generator.fake.random_int(min=100, max=500000)  # ₹100 to ₹5,00,000
            merchant = demo_generator.fake.company()
        
        # Determine if this should be labeled as fraud
        is_fraud = demo_generator.fake.boolean(chance_of_getting_true=15)  # 15% fraud rate
        
        # Adjust patterns for fraud cases
        if is_fraud:
            # Make fraudulent activities more suspicious
            if activity_type == 'transaction':
                amount = demo_generator.fake.random_int(min=50000, max=1000000)  # Higher amounts
            
            # Unusual timing
            if demo_generator.fake.boolean(chance_of_getting_true=30):
                timestamp = timestamp.replace(hour=demo_generator.fake.random_int(min=0, max=5))
        
        # Create activity record
        activity_data = {
            'user_id': user_id,
            'action_type': activity_type,
            'timestamp': timestamp,
            'ip_address': demo_generator.fake.ipv4(),
            'user_agent': demo_generator.fake.user_agent(),
            'amount': amount,
            'merchant': merchant,
            'is_fraud': is_fraud,
            'risk_level': 'high' if is_fraud else demo_generator.fake.random_element(['low', 'medium'])
        }
        
        # Store in database
        try:
            db.store_labeled_activity(activity_data)
        except AttributeError:
            # If method doesn't exist, store as regular activity
            db.store_activity(
                user_id=user_id,
                action_type=activity_type,
                trust_score=20.0 if is_fraud else 80.0,
                risk_level=activity_data['risk_level'],
                decision='block' if is_fraud else 'allow',
                context=str(activity_data),
                ip_address=activity_data['ip_address'],
                user_agent=activity_data['user_agent']
            )
    
    logger.info(f"Generated {num_samples} training samples successfully")

def train_models(db: Database, retrain: bool = False) -> None:
    """
    Train ML models
    """
    logger.info("Starting model training process...")
    
    # Initialize components
    feature_engineer = FeatureEngineer(db)
    ml_detector = MLFraudDetector(db, feature_engineer)
    
    # Train models
    results = ml_detector.train_models(retrain=retrain)
    
    if results['status'] == 'success':
        logger.info("Model training completed successfully!")
        
        # Print results
        for model_name, result in results['results'].items():
            if 'error' not in result:
                logger.info(f"\n{model_name.upper()} Results:")
                logger.info(f"  Accuracy: {result.get('accuracy', 0):.3f}")
                logger.info(f"  Precision: {result.get('precision', 0):.3f}")
                logger.info(f"  Recall: {result.get('recall', 0):.3f}")
                logger.info(f"  F1 Score: {result.get('f1_score', 0):.3f}")
                logger.info(f"  AUC-ROC: {result.get('auc_roc', 0):.3f}")
                
                # Top features
                if 'feature_importance' in result:
                    logger.info("  Top Features:")
                    for feature, importance in list(result['feature_importance'].items())[:5]:
                        logger.info(f"    {feature}: {importance:.3f}")
            else:
                logger.error(f"{model_name} training failed: {result['error']}")
    
    elif results['status'] == 'insufficient_data':
        logger.warning(f"Insufficient training data: {results['samples']} samples")
        logger.info("Generating more training data...")
        generate_training_data(db, num_samples=2000)
        
        # Retry training
        logger.info("Retrying model training with new data...")
        results = ml_detector.train_models(retrain=True)
        
        if results['status'] == 'success':
            logger.info("Model training completed after data generation!")
        else:
            logger.error(f"Model training failed: {results}")
    
    else:
        logger.error(f"Model training failed: {results}")

def evaluate_models(db: Database) -> None:
    """
    Evaluate trained models on test data
    """
    logger.info("Evaluating trained models...")
    
    feature_engineer = FeatureEngineer(db)
    ml_detector = MLFraudDetector(db, feature_engineer)
    
    # Get model info
    model_info = ml_detector.get_model_info()
    logger.info(f"Loaded models: {model_info['loaded_models']}")
    logger.info(f"Model version: {model_info['model_version']}")
    
    if model_info['last_training']:
        logger.info(f"Last training: {model_info['last_training']}")
    
    # Test predictions on sample data
    test_contexts = [
        {
            'user_id': 1,
            'action': 'transaction',
            'timestamp': datetime.now(),
            'ip_address': '***********',
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'amount': 85280,  # ₹85,280
            'merchant': 'Amazon'
        },
        {
            'user_id': 2,
            'action': 'login',
            'timestamp': datetime.now().replace(hour=2),  # Late night
            'ip_address': '********',
            'user_agent': 'curl/7.68.0',  # Suspicious user agent
            'amount': 0,
            'merchant': ''
        }
    ]
    
    for i, context in enumerate(test_contexts):
        logger.info(f"\nTest Case {i+1}:")
        logger.info(f"  Context: {context}")
        
        try:
            prediction = ml_detector.predict_fraud_probability(context, context['user_id'])
            logger.info(f"  Trust Score: {prediction['trust_score']}")
            logger.info(f"  Fraud Probability: {prediction['fraud_probability']:.3f}")
            logger.info(f"  Risk Level: {prediction['risk_level']}")
            logger.info(f"  Anomaly Score: {prediction['anomaly_score']:.3f}")
            
            if prediction['model_predictions']:
                logger.info("  Model Predictions:")
                for model, prob in prediction['model_predictions'].items():
                    logger.info(f"    {model}: {prob:.3f}")
        
        except Exception as e:
            logger.error(f"  Prediction failed: {str(e)}")

def main():
    """
    Main training script
    """
    parser = argparse.ArgumentParser(description='TrustAI Model Training')
    parser.add_argument('--action', choices=['train', 'evaluate', 'generate-data', 'all'], 
                       default='all', help='Action to perform')
    parser.add_argument('--retrain', action='store_true', 
                       help='Force retrain even if models exist')
    parser.add_argument('--samples', type=int, default=5000,
                       help='Number of training samples to generate')
    parser.add_argument('--db-path', default='trustai.db',
                       help='Database path')
    
    args = parser.parse_args()
    
    logger.info("TrustAI Model Training Script")
    logger.info("=" * 50)
    
    # Initialize database
    try:
        db = Database(args.db_path)
        logger.info(f"Connected to database: {args.db_path}")
    except Exception as e:
        logger.error(f"Database connection failed: {str(e)}")
        return 1
    
    try:
        if args.action in ['generate-data', 'all']:
            generate_training_data(db, args.samples)
        
        if args.action in ['train', 'all']:
            train_models(db, args.retrain)
        
        if args.action in ['evaluate', 'all']:
            evaluate_models(db)
        
        logger.info("Training script completed successfully!")
        return 0
        
    except Exception as e:
        logger.error(f"Training script failed: {str(e)}")
        return 1

if __name__ == '__main__':
    exit(main())
