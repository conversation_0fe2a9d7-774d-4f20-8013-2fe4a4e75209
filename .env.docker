# TrustAI Docker Environment Configuration

# Application Configuration
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# Security Configuration
JWT_SECRET_KEY=trustai-docker-secret-key-change-in-production
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900
SESSION_TIMEOUT=3600

# Database Configuration (SQLite for development)
DATABASE_URL=sqlite:///trustai.db

# Fraud Detection Thresholds
HIGH_RISK_THRESHOLD=40
MEDIUM_RISK_THRESHOLD=70
MAX_TRANSACTION_AMOUNT=10000
MAX_DAILY_TRANSACTIONS=20

# Frontend Configuration
REACT_APP_API_URL=http://localhost:5000

# Production Database Configuration (when using PostgreSQL)
POSTGRES_DB=trustai
POSTGRES_USER=trustai
POSTGRES_PASSWORD=trustai
POSTGRES_HOST=db
POSTGRES_PORT=5432

# Redis Configuration (for production)
REDIS_URL=redis://redis:6379
REDIS_HOST=redis
REDIS_PORT=6379
