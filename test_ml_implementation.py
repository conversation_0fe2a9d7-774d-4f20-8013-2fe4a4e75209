#!/usr/bin/env python3
"""
TrustAI ML Implementation Test Script
Test the ML-enhanced fraud detection system
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import json

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

def test_feature_engineering():
    """Test feature engineering functionality"""
    print("🔧 Testing Feature Engineering...")
    
    try:
        from src.database import Database
        from src.ml_feature_engineering import FeatureEngineer
        
        # Initialize components
        db = Database()
        feature_engineer = FeatureEngineer(db)
        
        # Test context
        test_context = {
            'user_id': 1,
            'action': 'transaction',
            'timestamp': datetime.now(),
            'ip_address': '***********',
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'amount': 85280,  # ₹85,280
            'merchant': 'Amazon'
        }
        
        # Extract features
        features = feature_engineer.extract_features(test_context, 1)
        
        print(f"✅ Extracted {len(features)} features")
        print("📊 Sample features:")
        for i, (key, value) in enumerate(list(features.items())[:10]):
            print(f"   {key}: {value}")
        
        if len(features) >= 40:  # Should have many features
            print("✅ Feature engineering working correctly")
            return True
        else:
            print(f"❌ Expected 40+ features, got {len(features)}")
            return False
            
    except Exception as e:
        print(f"❌ Feature engineering test failed: {str(e)}")
        return False

def test_ml_models():
    """Test ML model functionality"""
    print("\n🤖 Testing ML Models...")
    
    try:
        from src.database import Database
        from src.ml_feature_engineering import FeatureEngineer
        from src.ml_models import MLFraudDetector
        
        # Initialize components
        db = Database()
        feature_engineer = FeatureEngineer(db)
        ml_detector = MLFraudDetector(db, feature_engineer)
        
        # Test prediction (should work even without trained models)
        test_context = {
            'user_id': 1,
            'action': 'transaction',
            'timestamp': datetime.now(),
            'ip_address': '***********',
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'amount': 85280,
            'merchant': 'Amazon'
        }
        
        # Get prediction
        prediction = ml_detector.predict_fraud_probability(test_context, 1)
        
        print("✅ ML prediction generated")
        print(f"📊 Trust Score: {prediction['trust_score']}")
        print(f"📊 Fraud Probability: {prediction['fraud_probability']}")
        print(f"📊 Risk Level: {prediction['risk_level']}")
        print(f"📊 Model Version: {prediction['model_version']}")
        
        # Check model info
        model_info = ml_detector.get_model_info()
        print(f"📊 Loaded Models: {model_info['loaded_models']}")
        
        return True
        
    except Exception as e:
        print(f"❌ ML models test failed: {str(e)}")
        return False

def test_hybrid_engine():
    """Test the hybrid ML + rule-based engine"""
    print("\n🔀 Testing Hybrid Trust Engine...")
    
    try:
        from src.database import Database
        from src.ml_trust_engine import MLTrustEngine
        
        # Initialize hybrid engine
        db = Database()
        trust_engine = MLTrustEngine(db)
        
        # Test contexts
        test_contexts = [
            {
                'user_id': 1,
                'action': 'login',
                'timestamp': datetime.now(),
                'ip_address': '***********',
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            {
                'user_id': 2,
                'action': 'transaction',
                'timestamp': datetime.now().replace(hour=2),  # Late night
                'ip_address': '********',
                'user_agent': 'curl/7.68.0',  # Suspicious
                'amount': 426400,  # ₹4,26,400 - high amount
                'merchant': 'Unknown Merchant'
            }
        ]
        
        for i, context in enumerate(test_contexts):
            print(f"\n📋 Test Case {i+1}: {context['action']}")
            
            result = trust_engine.analyze_activity(context)
            
            print(f"   Trust Score: {result['score']}")
            print(f"   Risk Level: {result['risk_level']}")
            print(f"   Decision: {result['decision']}")
            print(f"   Method: {result.get('prediction_method', 'unknown')}")
            
            if 'ml_insights' in result:
                insights = result['ml_insights']
                print(f"   ML Fraud Prob: {insights['fraud_probability']:.3f}")
                print(f"   Anomaly Score: {insights['anomaly_score']:.3f}")
        
        # Test performance metrics
        metrics = trust_engine.get_performance_metrics()
        print(f"\n📈 Performance Metrics:")
        print(f"   ML Predictions: {metrics['prediction_metrics']['ml_predictions']}")
        print(f"   Rule Predictions: {metrics['prediction_metrics']['rule_predictions']}")
        print(f"   Hybrid Predictions: {metrics['prediction_metrics']['hybrid_predictions']}")
        
        print("✅ Hybrid engine working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Hybrid engine test failed: {str(e)}")
        return False

def test_database_integration():
    """Test ML database integration"""
    print("\n💾 Testing Database Integration...")
    
    try:
        from src.database import Database
        
        db = Database()
        
        # Test ML metadata storage
        test_metadata = {
            'fraud_probability': 0.75,
            'anomaly_score': 0.6,
            'model_version': 'test_v1.0',
            'model_predictions': {'rf': 0.7, 'xgb': 0.8},
            'feature_importance': {'amount': 0.3, 'device': 0.2}
        }
        
        db.store_ml_metadata(1, test_metadata)
        print("✅ ML metadata storage working")
        
        # Test labeled activity storage
        test_activity = {
            'user_id': 1,
            'action_type': 'transaction',
            'timestamp': datetime.now(),
            'ip_address': '***********',
            'user_agent': 'test_agent',
            'amount': 1000,
            'merchant': 'Test Merchant',
            'is_fraud': False,
            'risk_level': 'low'
        }
        
        db.store_labeled_activity(test_activity)
        print("✅ Labeled activity storage working")
        
        # Test retrieval
        activities = db.get_labeled_activities(limit=5)
        print(f"✅ Retrieved {len(activities)} labeled activities")
        
        return True
        
    except Exception as e:
        print(f"❌ Database integration test failed: {str(e)}")
        return False

def test_training_pipeline():
    """Test the model training pipeline"""
    print("\n🎓 Testing Training Pipeline...")
    
    try:
        # Generate some test data first
        from src.database import Database
        from src.demo_data import DemoDataGenerator
        
        db = Database()
        demo_generator = DemoDataGenerator(db)
        
        # Generate a small amount of training data
        print("📊 Generating test training data...")
        for i in range(50):  # Small sample for testing
            user_id = 1 + (i % 5)  # Use 5 different users
            
            activity_data = {
                'user_id': user_id,
                'action_type': 'transaction',
                'timestamp': datetime.now(),
                'ip_address': f'192.168.1.{i % 10}',
                'user_agent': 'test_agent',
                'amount': 1000 + (i * 100),
                'merchant': f'Merchant_{i % 3}',
                'is_fraud': i % 10 == 0,  # 10% fraud rate
                'risk_level': 'high' if i % 10 == 0 else 'low'
            }
            
            db.store_labeled_activity(activity_data)
        
        print("✅ Test training data generated")
        
        # Test model training (this might fail due to insufficient data, which is expected)
        from src.ml_models import MLFraudDetector
        from src.ml_feature_engineering import FeatureEngineer
        
        feature_engineer = FeatureEngineer(db)
        ml_detector = MLFraudDetector(db, feature_engineer)
        
        print("🎓 Attempting model training...")
        results = ml_detector.train_models()
        
        if results['status'] == 'success':
            print("✅ Model training successful")
            for model_name, result in results['results'].items():
                if 'error' not in result:
                    print(f"   {model_name}: Accuracy = {result.get('accuracy', 0):.3f}")
        elif results['status'] == 'insufficient_data':
            print(f"⚠️  Insufficient training data (expected for test): {results['samples']} samples")
        else:
            print(f"❌ Training failed: {results}")
        
        return True
        
    except Exception as e:
        print(f"❌ Training pipeline test failed: {str(e)}")
        return False

def main():
    """Run all ML implementation tests"""
    print("🛡️ TrustAI ML Implementation Test Suite")
    print("=" * 50)
    
    # Initialize database
    try:
        from src.database import Database
        db = Database()
        db.init_db()
        print("✅ Database initialized")
    except Exception as e:
        print(f"❌ Database initialization failed: {str(e)}")
        return 1
    
    # Run tests
    tests = [
        ("Feature Engineering", test_feature_engineering),
        ("ML Models", test_ml_models),
        ("Hybrid Engine", test_hybrid_engine),
        ("Database Integration", test_database_integration),
        ("Training Pipeline", test_training_pipeline)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! ML implementation is working correctly.")
        print("\n🚀 Next steps:")
        print("   1. Generate more training data: python train_models.py --action generate-data --samples 2000")
        print("   2. Train models: python train_models.py --action train")
        print("   3. Start the application: python app.py")
        return 0
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
        return 1

if __name__ == '__main__':
    exit(main())
