"""
TrustAI ML Models
Advanced machine learning models for fraud detection
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
import logging
import joblib
import json
import os
from pathlib import Path

# ML Libraries
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, roc_auc_score, precision_recall_curve
import xgboost as xgb
import lightgbm as lgb
from sklearn.linear_model import LogisticRegression

logger = logging.getLogger(__name__)

class MLFraudDetector:
    """
    Advanced ML-based fraud detection system
    """
    
    def __init__(self, database, feature_engineer):
        self.db = database
        self.feature_engineer = feature_engineer
        self.models = {}
        self.scalers = {}
        self.model_metadata = {}
        self.model_dir = Path('models')
        self.model_dir.mkdir(exist_ok=True)
        
        # Model configuration
        self.model_configs = {
            'random_forest': {
                'n_estimators': 100,
                'max_depth': 10,
                'min_samples_split': 5,
                'random_state': 42
            },
            'xgboost': {
                'n_estimators': 100,
                'max_depth': 6,
                'learning_rate': 0.1,
                'random_state': 42
            },
            'lightgbm': {
                'n_estimators': 100,
                'max_depth': 6,
                'learning_rate': 0.1,
                'random_state': 42
            },
            'logistic_regression': {
                'random_state': 42,
                'max_iter': 1000
            }
        }
        
        # Load existing models if available
        self._load_models()
    
    def train_models(self, retrain: bool = False) -> Dict[str, Any]:
        """
        Train all ML models on historical data
        """
        try:
            logger.info("Starting ML model training...")
            
            # Prepare training data
            X, y, feature_names = self._prepare_training_data()
            
            if len(X) < 100:
                logger.warning("Insufficient training data. Need at least 100 samples.")
                return {'status': 'insufficient_data', 'samples': len(X)}
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            results = {}
            
            # Train each model
            for model_name in self.model_configs.keys():
                logger.info(f"Training {model_name}...")
                
                try:
                    # Train model
                    model, scaler = self._train_single_model(
                        model_name, X_train, y_train, feature_names
                    )
                    
                    # Evaluate model
                    evaluation = self._evaluate_model(
                        model, scaler, X_test, y_test, feature_names
                    )
                    
                    # Save model
                    self._save_model(model_name, model, scaler, feature_names, evaluation)
                    
                    results[model_name] = evaluation
                    
                except Exception as e:
                    logger.error(f"Error training {model_name}: {str(e)}")
                    results[model_name] = {'error': str(e)}
            
            # Train anomaly detection model
            try:
                anomaly_model = self._train_anomaly_detector(X_train)
                self._save_anomaly_model(anomaly_model)
                results['anomaly_detector'] = {'status': 'trained'}
            except Exception as e:
                logger.error(f"Error training anomaly detector: {str(e)}")
                results['anomaly_detector'] = {'error': str(e)}
            
            logger.info("Model training completed")
            return {'status': 'success', 'results': results}
            
        except Exception as e:
            logger.error(f"Model training failed: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def predict_fraud_probability(self, context: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """
        Predict fraud probability using ensemble of ML models
        """
        try:
            # Extract features
            features = self.feature_engineer.extract_features(context, user_id)
            
            if not self.models:
                logger.warning("No trained models available. Using fallback.")
                return self._fallback_prediction(features)
            
            # Get predictions from all models
            predictions = {}
            feature_importance = {}
            
            for model_name, model_data in self.models.items():
                try:
                    model = model_data['model']
                    scaler = model_data['scaler']
                    feature_names = model_data['feature_names']
                    
                    # Prepare feature vector
                    X = self._prepare_feature_vector(features, feature_names)
                    X_scaled = scaler.transform(X.reshape(1, -1))
                    
                    # Get prediction
                    if hasattr(model, 'predict_proba'):
                        prob = model.predict_proba(X_scaled)[0][1]  # Probability of fraud
                    else:
                        prob = model.decision_function(X_scaled)[0]
                        prob = 1 / (1 + np.exp(-prob))  # Sigmoid transformation
                    
                    predictions[model_name] = prob
                    
                    # Get feature importance if available
                    if hasattr(model, 'feature_importances_'):
                        importance = dict(zip(feature_names, model.feature_importances_))
                        feature_importance[model_name] = importance
                    
                except Exception as e:
                    logger.error(f"Error in {model_name} prediction: {str(e)}")
                    continue
            
            if not predictions:
                return self._fallback_prediction(features)
            
            # Ensemble prediction (weighted average)
            ensemble_prob = self._calculate_ensemble_prediction(predictions)
            
            # Anomaly detection
            anomaly_score = self._detect_anomaly(features)
            
            # Convert to trust score (inverse of fraud probability)
            trust_score = (1 - ensemble_prob) * 100
            
            # Adjust based on anomaly score
            if anomaly_score > 0.7:
                trust_score *= 0.8  # Reduce trust score for anomalies
            
            # Determine risk level
            risk_level = self._determine_risk_level(trust_score)
            
            return {
                'trust_score': round(trust_score, 2),
                'fraud_probability': round(ensemble_prob, 4),
                'risk_level': risk_level,
                'model_predictions': predictions,
                'anomaly_score': round(anomaly_score, 4),
                'feature_importance': feature_importance,
                'model_version': self._get_model_version(),
                'prediction_timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"ML prediction error: {str(e)}")
            return self._fallback_prediction(features if 'features' in locals() else {})
    
    def _prepare_training_data(self) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        Prepare training data from historical activities
        """
        # Get historical activities with labels
        activities = self.db.get_labeled_activities(limit=10000)
        
        if not activities:
            raise ValueError("No labeled training data available")
        
        features_list = []
        labels = []
        
        for activity in activities:
            try:
                # Extract features for this activity
                context = {
                    'user_id': activity['user_id'],
                    'action': activity['action_type'],
                    'timestamp': activity['timestamp'],
                    'ip_address': activity.get('ip_address', ''),
                    'user_agent': activity.get('user_agent', ''),
                    'amount': activity.get('amount', 0),
                    'merchant': activity.get('merchant', '')
                }
                
                features = self.feature_engineer.extract_features(context, activity['user_id'])
                features_list.append(features)
                
                # Determine label (1 = fraud, 0 = legitimate)
                label = 1 if activity.get('is_fraud', False) or activity.get('risk_level') == 'high' else 0
                labels.append(label)
                
            except Exception as e:
                logger.warning(f"Error processing activity {activity.get('id')}: {str(e)}")
                continue
        
        if not features_list:
            raise ValueError("No valid features extracted from training data")
        
        # Convert to DataFrame for easier handling
        df = pd.DataFrame(features_list)
        feature_names = df.columns.tolist()
        
        # Handle missing values
        df = df.fillna(0)
        
        # Convert to numpy arrays
        X = df.values
        y = np.array(labels)
        
        logger.info(f"Prepared training data: {len(X)} samples, {len(feature_names)} features")
        logger.info(f"Class distribution: {np.bincount(y)}")
        
        return X, y, feature_names
    
    def _train_single_model(self, model_name: str, X_train: np.ndarray, 
                           y_train: np.ndarray, feature_names: List[str]) -> Tuple[Any, Any]:
        """
        Train a single ML model
        """
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        
        # Initialize model
        config = self.model_configs[model_name]
        
        if model_name == 'random_forest':
            model = RandomForestClassifier(**config)
        elif model_name == 'xgboost':
            model = xgb.XGBClassifier(**config)
        elif model_name == 'lightgbm':
            model = lgb.LGBMClassifier(**config)
        elif model_name == 'logistic_regression':
            model = LogisticRegression(**config)
        else:
            raise ValueError(f"Unknown model: {model_name}")
        
        # Train model
        model.fit(X_train_scaled, y_train)
        
        return model, scaler
    
    def _evaluate_model(self, model: Any, scaler: Any, X_test: np.ndarray, 
                       y_test: np.ndarray, feature_names: List[str]) -> Dict[str, Any]:
        """
        Evaluate model performance
        """
        X_test_scaled = scaler.transform(X_test)
        
        # Predictions
        y_pred = model.predict(X_test_scaled)
        y_pred_proba = model.predict_proba(X_test_scaled)[:, 1] if hasattr(model, 'predict_proba') else None
        
        # Metrics
        evaluation = {
            'accuracy': float(np.mean(y_pred == y_test)),
            'precision': float(np.sum((y_pred == 1) & (y_test == 1)) / max(1, np.sum(y_pred == 1))),
            'recall': float(np.sum((y_pred == 1) & (y_test == 1)) / max(1, np.sum(y_test == 1))),
            'f1_score': 0.0,
            'auc_roc': 0.0
        }
        
        # F1 Score
        precision = evaluation['precision']
        recall = evaluation['recall']
        evaluation['f1_score'] = float(2 * precision * recall / max(1e-6, precision + recall))
        
        # AUC-ROC
        if y_pred_proba is not None and len(np.unique(y_test)) > 1:
            evaluation['auc_roc'] = float(roc_auc_score(y_test, y_pred_proba))
        
        # Feature importance
        if hasattr(model, 'feature_importances_'):
            importance = dict(zip(feature_names, model.feature_importances_))
            evaluation['feature_importance'] = {k: float(v) for k, v in 
                                              sorted(importance.items(), key=lambda x: x[1], reverse=True)[:10]}
        
        return evaluation
    
    def _train_anomaly_detector(self, X_train: np.ndarray) -> IsolationForest:
        """
        Train anomaly detection model
        """
        anomaly_model = IsolationForest(
            contamination=0.1,  # Expect 10% anomalies
            random_state=42,
            n_estimators=100
        )
        
        anomaly_model.fit(X_train)
        return anomaly_model
    
    def _calculate_ensemble_prediction(self, predictions: Dict[str, float]) -> float:
        """
        Calculate ensemble prediction from individual model predictions
        """
        if not predictions:
            return 0.5
        
        # Weighted average (can be improved with learned weights)
        weights = {
            'random_forest': 0.3,
            'xgboost': 0.3,
            'lightgbm': 0.25,
            'logistic_regression': 0.15
        }
        
        weighted_sum = 0.0
        total_weight = 0.0
        
        for model_name, prediction in predictions.items():
            weight = weights.get(model_name, 0.1)
            weighted_sum += prediction * weight
            total_weight += weight
        
        return weighted_sum / max(total_weight, 1e-6)
    
    def _detect_anomaly(self, features: Dict[str, float]) -> float:
        """
        Detect anomalies using isolation forest
        """
        try:
            if 'anomaly_detector' not in self.models:
                return 0.0
            
            anomaly_model = self.models['anomaly_detector']['model']
            scaler = self.models['anomaly_detector']['scaler']
            feature_names = self.models['anomaly_detector']['feature_names']
            
            # Prepare feature vector
            X = self._prepare_feature_vector(features, feature_names)
            X_scaled = scaler.transform(X.reshape(1, -1))
            
            # Get anomaly score
            anomaly_score = anomaly_model.decision_function(X_scaled)[0]
            
            # Convert to probability (higher = more anomalous)
            return max(0.0, min(1.0, (0.5 - anomaly_score) / 0.5))
            
        except Exception as e:
            logger.error(f"Anomaly detection error: {str(e)}")
            return 0.0
    
    def _prepare_feature_vector(self, features: Dict[str, float], feature_names: List[str]) -> np.ndarray:
        """
        Prepare feature vector for model input
        """
        feature_vector = []
        for name in feature_names:
            feature_vector.append(features.get(name, 0.0))
        return np.array(feature_vector)
    
    def _determine_risk_level(self, trust_score: float) -> str:
        """
        Determine risk level from trust score
        """
        if trust_score >= 70:
            return 'low'
        elif trust_score >= 40:
            return 'medium'
        else:
            return 'high'
    
    def _fallback_prediction(self, features: Dict[str, float]) -> Dict[str, Any]:
        """
        Fallback prediction when ML models are not available
        """
        return {
            'trust_score': 70.0,
            'fraud_probability': 0.3,
            'risk_level': 'medium',
            'model_predictions': {},
            'anomaly_score': 0.0,
            'feature_importance': {},
            'model_version': 'fallback',
            'prediction_timestamp': datetime.utcnow().isoformat()
        }
    
    def _save_model(self, model_name: str, model: Any, scaler: Any, 
                   feature_names: List[str], evaluation: Dict[str, Any]) -> None:
        """
        Save trained model to disk
        """
        model_path = self.model_dir / f"{model_name}.joblib"
        scaler_path = self.model_dir / f"{model_name}_scaler.joblib"
        metadata_path = self.model_dir / f"{model_name}_metadata.json"
        
        # Save model and scaler
        joblib.dump(model, model_path)
        joblib.dump(scaler, scaler_path)
        
        # Save metadata
        metadata = {
            'feature_names': feature_names,
            'evaluation': evaluation,
            'training_timestamp': datetime.utcnow().isoformat(),
            'model_version': '1.0'
        }
        
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        # Update in-memory models
        self.models[model_name] = {
            'model': model,
            'scaler': scaler,
            'feature_names': feature_names,
            'metadata': metadata
        }
        
        logger.info(f"Saved model: {model_name}")
    
    def _save_anomaly_model(self, anomaly_model: IsolationForest) -> None:
        """
        Save anomaly detection model
        """
        model_path = self.model_dir / "anomaly_detector.joblib"
        joblib.dump(anomaly_model, model_path)
        
        # Use same scaler and features as main models
        if self.models:
            first_model = next(iter(self.models.values()))
            self.models['anomaly_detector'] = {
                'model': anomaly_model,
                'scaler': first_model['scaler'],
                'feature_names': first_model['feature_names'],
                'metadata': {'type': 'anomaly_detector'}
            }
    
    def _load_models(self) -> None:
        """
        Load existing models from disk
        """
        try:
            for model_name in self.model_configs.keys():
                model_path = self.model_dir / f"{model_name}.joblib"
                scaler_path = self.model_dir / f"{model_name}_scaler.joblib"
                metadata_path = self.model_dir / f"{model_name}_metadata.json"
                
                if all(path.exists() for path in [model_path, scaler_path, metadata_path]):
                    model = joblib.load(model_path)
                    scaler = joblib.load(scaler_path)
                    
                    with open(metadata_path, 'r') as f:
                        metadata = json.load(f)
                    
                    self.models[model_name] = {
                        'model': model,
                        'scaler': scaler,
                        'feature_names': metadata['feature_names'],
                        'metadata': metadata
                    }
                    
                    logger.info(f"Loaded model: {model_name}")
            
            # Load anomaly detector
            anomaly_path = self.model_dir / "anomaly_detector.joblib"
            if anomaly_path.exists() and self.models:
                anomaly_model = joblib.load(anomaly_path)
                first_model = next(iter(self.models.values()))
                self.models['anomaly_detector'] = {
                    'model': anomaly_model,
                    'scaler': first_model['scaler'],
                    'feature_names': first_model['feature_names'],
                    'metadata': {'type': 'anomaly_detector'}
                }
                logger.info("Loaded anomaly detector")
                
        except Exception as e:
            logger.error(f"Error loading models: {str(e)}")
    
    def _get_model_version(self) -> str:
        """
        Get current model version
        """
        if not self.models:
            return 'no_models'
        
        versions = []
        for model_data in self.models.values():
            if 'metadata' in model_data and 'model_version' in model_data['metadata']:
                versions.append(model_data['metadata']['model_version'])
        
        return f"ensemble_v{len(set(versions))}" if versions else 'unknown'
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about loaded models
        """
        info = {
            'loaded_models': list(self.models.keys()),
            'model_count': len(self.models),
            'model_version': self._get_model_version(),
            'last_training': None
        }
        
        # Get last training timestamp
        training_times = []
        for model_data in self.models.values():
            if 'metadata' in model_data and 'training_timestamp' in model_data['metadata']:
                training_times.append(model_data['metadata']['training_timestamp'])
        
        if training_times:
            info['last_training'] = max(training_times)
        
        return info
