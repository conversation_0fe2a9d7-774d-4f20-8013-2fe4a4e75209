"""
TrustAI ML Feature Engineering
Advanced feature extraction and preprocessing for machine learning models
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
import logging
import hashlib
import json
from collections import defaultdict
import re

logger = logging.getLogger(__name__)

class FeatureEngineer:
    """
    Advanced feature engineering for fraud detection ML models
    """
    
    def __init__(self, database):
        self.db = database
        self.feature_cache = {}
        
    def extract_features(self, context: Dict[str, Any], user_id: int) -> Dict[str, float]:
        """
        Extract comprehensive feature set for ML models
        """
        try:
            features = {}
            
            # Basic context features
            features.update(self._extract_basic_features(context))
            
            # Device and fingerprinting features
            features.update(self._extract_device_features(context, user_id))
            
            # Transaction and velocity features
            features.update(self._extract_transaction_features(context, user_id))
            
            # Behavioral and temporal features
            features.update(self._extract_behavioral_features(context, user_id))
            
            # Geolocation features
            features.update(self._extract_geolocation_features(context, user_id))
            
            # Account and historical features
            features.update(self._extract_account_features(context, user_id))
            
            # Network and graph features
            features.update(self._extract_network_features(context, user_id))
            
            # Risk aggregation features
            features.update(self._extract_risk_aggregation_features(context, user_id))
            
            return features
            
        except Exception as e:
            logger.error(f"Feature extraction error: {str(e)}")
            return self._get_default_features()
    
    def _extract_basic_features(self, context: Dict[str, Any]) -> Dict[str, float]:
        """Extract basic contextual features"""
        features = {}
        
        # Time-based features
        timestamp = context.get('timestamp', datetime.utcnow())
        features['hour_of_day'] = timestamp.hour
        features['day_of_week'] = timestamp.weekday()
        features['is_weekend'] = 1.0 if timestamp.weekday() >= 5 else 0.0
        features['is_business_hours'] = 1.0 if 9 <= timestamp.hour <= 17 else 0.0
        features['is_late_night'] = 1.0 if timestamp.hour >= 23 or timestamp.hour <= 5 else 0.0
        
        # Action type encoding
        action_types = ['login', 'transaction', 'profile_update', 'password_change']
        action = context.get('action', 'unknown')
        for action_type in action_types:
            features[f'action_{action_type}'] = 1.0 if action == action_type else 0.0
        
        # Amount features (for transactions)
        amount = context.get('amount', 0)
        features['transaction_amount'] = float(amount)
        features['amount_log'] = np.log1p(amount)
        features['is_high_value'] = 1.0 if amount > 85280 else 0.0  # ₹85,280
        features['is_micro_transaction'] = 1.0 if 0 < amount < 852.8 else 0.0  # ₹852.8
        
        return features
    
    def _extract_device_features(self, context: Dict[str, Any], user_id: int) -> Dict[str, float]:
        """Extract device and fingerprinting features"""
        features = {}
        
        # User agent analysis
        user_agent = context.get('user_agent', '')
        features['user_agent_length'] = len(user_agent)
        features['is_mobile'] = 1.0 if any(mobile in user_agent.lower() 
                                         for mobile in ['mobile', 'android', 'iphone']) else 0.0
        features['is_bot'] = 1.0 if any(bot in user_agent.lower() 
                                      for bot in ['bot', 'crawler', 'spider']) else 0.0
        
        # Device fingerprint analysis
        fingerprint = self._generate_device_fingerprint(context)
        features['device_fingerprint_entropy'] = self._calculate_entropy(fingerprint)
        
        # Device history features
        device_history = self.db.get_user_devices(user_id, days=30)
        features['unique_devices_30d'] = len(set(d['fingerprint'] for d in device_history))
        features['device_first_seen'] = 1.0 if not device_history else 0.0
        
        # Device consistency
        if device_history:
            current_fp = fingerprint
            similarities = [self._calculate_device_similarity(current_fp, d['fingerprint']) 
                          for d in device_history]
            features['max_device_similarity'] = max(similarities) if similarities else 0.0
            features['avg_device_similarity'] = np.mean(similarities) if similarities else 0.0
        else:
            features['max_device_similarity'] = 0.0
            features['avg_device_similarity'] = 0.0
        
        return features
    
    def _extract_transaction_features(self, context: Dict[str, Any], user_id: int) -> Dict[str, float]:
        """Extract transaction velocity and pattern features"""
        features = {}
        
        if context.get('action') != 'transaction':
            # Return zero features for non-transactions
            return {f'txn_{key}': 0.0 for key in [
                'count_1h', 'count_24h', 'count_7d', 'amount_1h', 'amount_24h', 'amount_7d',
                'avg_amount_7d', 'velocity_score', 'merchant_frequency', 'time_since_last'
            ]}
        
        current_time = context.get('timestamp', datetime.utcnow())
        amount = context.get('amount', 0)
        merchant = context.get('merchant', '')
        
        # Get transaction history
        transactions_1h = self.db.get_user_transactions_since(user_id, current_time - timedelta(hours=1))
        transactions_24h = self.db.get_user_transactions_since(user_id, current_time - timedelta(hours=24))
        transactions_7d = self.db.get_user_transactions_since(user_id, current_time - timedelta(days=7))
        
        # Count features
        features['txn_count_1h'] = len(transactions_1h)
        features['txn_count_24h'] = len(transactions_24h)
        features['txn_count_7d'] = len(transactions_7d)
        
        # Amount features
        features['txn_amount_1h'] = sum(t.get('amount', 0) for t in transactions_1h)
        features['txn_amount_24h'] = sum(t.get('amount', 0) for t in transactions_24h)
        features['txn_amount_7d'] = sum(t.get('amount', 0) for t in transactions_7d)
        
        # Average and statistical features
        if transactions_7d:
            amounts_7d = [t.get('amount', 0) for t in transactions_7d]
            features['txn_avg_amount_7d'] = np.mean(amounts_7d)
            features['txn_std_amount_7d'] = np.std(amounts_7d)
            features['txn_amount_zscore'] = (amount - np.mean(amounts_7d)) / (np.std(amounts_7d) + 1e-6)
        else:
            features['txn_avg_amount_7d'] = 0.0
            features['txn_std_amount_7d'] = 0.0
            features['txn_amount_zscore'] = 0.0
        
        # Velocity score (transactions per hour)
        features['txn_velocity_score'] = len(transactions_1h) + (len(transactions_24h) / 24)
        
        # Merchant frequency
        merchant_counts = defaultdict(int)
        for txn in transactions_7d:
            merchant_counts[txn.get('merchant', '')] += 1
        features['txn_merchant_frequency'] = merchant_counts.get(merchant, 0)
        features['txn_unique_merchants_7d'] = len(merchant_counts)
        
        # Time since last transaction
        if transactions_24h:
            last_txn_time = max(t.get('timestamp', current_time) for t in transactions_24h)
            features['txn_time_since_last'] = (current_time - last_txn_time).total_seconds() / 3600
        else:
            features['txn_time_since_last'] = 24.0  # Default to 24 hours
        
        return features
    
    def _extract_behavioral_features(self, context: Dict[str, Any], user_id: int) -> Dict[str, float]:
        """Extract behavioral pattern features"""
        features = {}
        
        # Get user's activity history
        activities = self.db.get_user_activities(user_id, limit=100)
        
        if not activities:
            return {f'behavior_{key}': 0.0 for key in [
                'session_length', 'actions_per_session', 'typical_hour_match',
                'activity_regularity', 'pattern_deviation'
            ]}
        
        # Session analysis
        current_time = context.get('timestamp', datetime.utcnow())
        session_activities = [a for a in activities 
                            if (current_time - a.get('timestamp', current_time)).total_seconds() < 3600]
        
        features['behavior_session_length'] = len(session_activities)
        features['behavior_actions_per_session'] = len(session_activities) / max(1, len(set(
            a.get('timestamp', current_time).date() for a in activities[-30:]
        )))
        
        # Time pattern analysis
        activity_hours = [a.get('timestamp', current_time).hour for a in activities[-50:]]
        current_hour = current_time.hour
        
        if activity_hours:
            hour_counts = defaultdict(int)
            for hour in activity_hours:
                hour_counts[hour] += 1
            
            most_common_hours = sorted(hour_counts.keys(), key=lambda x: hour_counts[x], reverse=True)[:3]
            features['behavior_typical_hour_match'] = 1.0 if current_hour in most_common_hours else 0.0
            
            # Activity regularity (entropy of hour distribution)
            hour_probs = np.array(list(hour_counts.values())) / sum(hour_counts.values())
            features['behavior_activity_regularity'] = -np.sum(hour_probs * np.log2(hour_probs + 1e-6))
        else:
            features['behavior_typical_hour_match'] = 0.0
            features['behavior_activity_regularity'] = 0.0
        
        # Pattern deviation score
        recent_pattern = self._extract_behavior_pattern(context)
        historical_patterns = [self._extract_behavior_pattern({'timestamp': a.get('timestamp'), 
                                                             'action': a.get('action_type')}) 
                             for a in activities[-20:]]
        
        if historical_patterns:
            deviations = [self._calculate_pattern_distance(recent_pattern, hp) for hp in historical_patterns]
            features['behavior_pattern_deviation'] = np.mean(deviations)
        else:
            features['behavior_pattern_deviation'] = 0.5
        
        return features
    
    def _extract_geolocation_features(self, context: Dict[str, Any], user_id: int) -> Dict[str, float]:
        """Extract geolocation-based features"""
        features = {}
        
        current_ip = context.get('ip_address', '')
        if not current_ip:
            return {f'geo_{key}': 0.0 for key in [
                'new_location', 'distance_from_usual', 'impossible_travel',
                'country_risk', 'vpn_probability'
            ]}
        
        # Get location history
        location_history = self.db.get_user_locations(user_id, days=30)
        current_location = self._get_location_from_ip(current_ip)
        
        # New location detection
        features['geo_new_location'] = 1.0 if not any(
            self._calculate_distance(current_location, loc) < 50 for loc in location_history
        ) else 0.0
        
        # Distance from usual locations
        if location_history:
            distances = [self._calculate_distance(current_location, loc) for loc in location_history]
            features['geo_distance_from_usual'] = min(distances)
            features['geo_avg_distance_from_usual'] = np.mean(distances)
        else:
            features['geo_distance_from_usual'] = 0.0
            features['geo_avg_distance_from_usual'] = 0.0
        
        # Impossible travel detection
        features['geo_impossible_travel'] = 0.0
        if location_history:
            latest_location = max(location_history, key=lambda x: x.get('timestamp', datetime.min))
            time_diff = (context.get('timestamp', datetime.utcnow()) - 
                        latest_location.get('timestamp', datetime.utcnow())).total_seconds()
            distance = self._calculate_distance(current_location, latest_location)
            
            if time_diff > 0:
                speed = (distance / 1000) / (time_diff / 3600)  # km/h
                features['geo_impossible_travel'] = 1.0 if speed > 1000 else 0.0
                features['geo_travel_speed'] = min(speed, 2000)  # Cap at 2000 km/h
            else:
                features['geo_travel_speed'] = 0.0
        
        # Country and VPN risk (simplified)
        features['geo_country_risk'] = self._get_country_risk_score(current_location.get('country', ''))
        features['geo_vpn_probability'] = self._estimate_vpn_probability(current_ip)
        
        return features
    
    def _extract_account_features(self, context: Dict[str, Any], user_id: int) -> Dict[str, float]:
        """Extract account history and profile features"""
        features = {}
        
        user_info = self.db.get_user(user_id)
        if not user_info:
            return {f'account_{key}': 0.0 for key in [
                'age_days', 'verification_score', 'incident_count', 'activity_score'
            ]}
        
        # Account age
        try:
            if isinstance(user_info['created_at'], str):
                created_at = datetime.fromisoformat(user_info['created_at'].replace('Z', '+00:00'))
            else:
                created_at = user_info['created_at']
            account_age = (datetime.utcnow() - created_at).days
        except:
            account_age = 1
        
        features['account_age_days'] = account_age
        features['account_age_log'] = np.log1p(account_age)
        features['account_is_new'] = 1.0 if account_age < 7 else 0.0
        
        # Verification and trust indicators
        features['account_verification_score'] = float(user_info.get('verified', 0))
        features['account_email_verified'] = float(user_info.get('email_verified', 0))
        features['account_phone_verified'] = float(user_info.get('phone_verified', 0))
        
        # Incident history
        incidents = self.db.get_user_incidents(user_id)
        features['account_incident_count'] = len(incidents)
        features['account_recent_incidents'] = len([i for i in incidents 
                                                  if (datetime.utcnow() - i.get('timestamp', datetime.min)).days < 30])
        
        # Activity score
        features['account_activity_score'] = self.db.get_user_activity_score(user_id)
        
        return features
    
    def _extract_network_features(self, context: Dict[str, Any], user_id: int) -> Dict[str, float]:
        """Extract network and graph-based features"""
        features = {}
        
        # Device sharing analysis
        current_fingerprint = self._generate_device_fingerprint(context)
        device_users = self.db.get_device_users(current_fingerprint)
        features['network_device_user_count'] = len(device_users)
        features['network_device_shared'] = 1.0 if len(device_users) > 1 else 0.0
        
        # IP sharing analysis
        current_ip = context.get('ip_address', '')
        if current_ip:
            ip_users = self.db.get_ip_users(current_ip)
            features['network_ip_user_count'] = len(ip_users)
            features['network_ip_shared'] = 1.0 if len(ip_users) > 1 else 0.0
        else:
            features['network_ip_user_count'] = 0.0
            features['network_ip_shared'] = 0.0
        
        # Connected component analysis (simplified)
        connected_users = self.db.get_connected_users(user_id)
        features['network_connected_users'] = len(connected_users)
        
        # Risk propagation from network
        if connected_users:
            connected_risk_scores = [self.db.get_user_risk_score(uid) for uid in connected_users]
            features['network_avg_risk'] = np.mean([score for score in connected_risk_scores if score is not None])
            features['network_max_risk'] = max([score for score in connected_risk_scores if score is not None], default=0.0)
        else:
            features['network_avg_risk'] = 0.0
            features['network_max_risk'] = 0.0
        
        return features
    
    def _extract_risk_aggregation_features(self, context: Dict[str, Any], user_id: int) -> Dict[str, float]:
        """Extract aggregated risk features"""
        features = {}
        
        # Historical trust score statistics
        trust_scores = self.db.get_trust_score_history(user_id, limit=30)
        if trust_scores:
            scores = [score['score'] for score in trust_scores]
            features['risk_avg_trust_score'] = np.mean(scores)
            features['risk_min_trust_score'] = min(scores)
            features['risk_trust_score_trend'] = scores[-1] - scores[0] if len(scores) > 1 else 0.0
            features['risk_trust_score_volatility'] = np.std(scores)
        else:
            features['risk_avg_trust_score'] = 70.0
            features['risk_min_trust_score'] = 70.0
            features['risk_trust_score_trend'] = 0.0
            features['risk_trust_score_volatility'] = 0.0
        
        # Risk factor aggregations
        recent_activities = self.db.get_user_activities(user_id, limit=20)
        if recent_activities:
            risk_levels = [a.get('risk_level', 'medium') for a in recent_activities]
            features['risk_high_risk_ratio'] = risk_levels.count('high') / len(risk_levels)
            features['risk_low_risk_ratio'] = risk_levels.count('low') / len(risk_levels)
        else:
            features['risk_high_risk_ratio'] = 0.0
            features['risk_low_risk_ratio'] = 0.0
        
        return features
    
    # Helper methods
    def _generate_device_fingerprint(self, context: Dict[str, Any]) -> str:
        """Generate device fingerprint"""
        user_agent = context.get('user_agent', '')
        ip_address = context.get('ip_address', '')
        fingerprint_data = f"{user_agent}:{ip_address}"
        return hashlib.md5(fingerprint_data.encode()).hexdigest()
    
    def _calculate_entropy(self, text: str) -> float:
        """Calculate entropy of a string"""
        if not text:
            return 0.0
        char_counts = defaultdict(int)
        for char in text:
            char_counts[char] += 1
        
        total_chars = len(text)
        entropy = 0.0
        for count in char_counts.values():
            prob = count / total_chars
            entropy -= prob * np.log2(prob)
        
        return entropy
    
    def _calculate_device_similarity(self, fp1: str, fp2: str) -> float:
        """Calculate similarity between device fingerprints"""
        if fp1 == fp2:
            return 1.0
        common_chars = sum(1 for a, b in zip(fp1, fp2) if a == b)
        return common_chars / max(len(fp1), len(fp2))
    
    def _get_location_from_ip(self, ip_address: str) -> Dict[str, Any]:
        """Get location from IP address"""
        # Simplified - in production use GeoIP2
        return {
            'latitude': 40.7128,
            'longitude': -74.0060,
            'city': 'New York',
            'country': 'US'
        }
    
    def _calculate_distance(self, loc1: Dict[str, float], loc2: Dict[str, float]) -> float:
        """Calculate distance between locations in km"""
        lat_diff = abs(loc1.get('latitude', 0) - loc2.get('latitude', 0))
        lon_diff = abs(loc1.get('longitude', 0) - loc2.get('longitude', 0))
        return ((lat_diff ** 2 + lon_diff ** 2) ** 0.5) * 111
    
    def _extract_behavior_pattern(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract behavior pattern from context"""
        timestamp = context.get('timestamp', datetime.utcnow())
        return {
            'hour': timestamp.hour,
            'day_of_week': timestamp.weekday(),
            'action': context.get('action', ''),
            'amount_bucket': self._get_amount_bucket(context.get('amount', 0))
        }
    
    def _calculate_pattern_distance(self, pattern1: Dict[str, Any], pattern2: Dict[str, Any]) -> float:
        """Calculate distance between behavior patterns"""
        distance = 0.0
        
        # Hour difference (circular)
        hour_diff = min(abs(pattern1['hour'] - pattern2['hour']), 
                       24 - abs(pattern1['hour'] - pattern2['hour']))
        distance += hour_diff / 12.0
        
        # Day of week difference
        distance += abs(pattern1['day_of_week'] - pattern2['day_of_week']) / 7.0
        
        # Action match
        distance += 0.0 if pattern1['action'] == pattern2['action'] else 1.0
        
        # Amount bucket match
        distance += 0.0 if pattern1['amount_bucket'] == pattern2['amount_bucket'] else 0.5
        
        return distance / 4.0  # Normalize
    
    def _get_amount_bucket(self, amount: float) -> str:
        """Get amount bucket for pattern analysis"""
        if amount == 0:
            return 'zero'
        elif amount < 852.8:  # ₹852.8
            return 'micro'
        elif amount < 8528:  # ₹8,528
            return 'small'
        elif amount < 85280:  # ₹85,280
            return 'medium'
        else:
            return 'large'
    
    def _get_country_risk_score(self, country: str) -> float:
        """Get risk score for country (simplified)"""
        high_risk_countries = ['XX', 'YY', 'ZZ']  # Placeholder
        return 0.8 if country in high_risk_countries else 0.2
    
    def _estimate_vpn_probability(self, ip_address: str) -> float:
        """Estimate VPN probability (simplified)"""
        # In production, use VPN detection services
        return 0.1  # Default low probability
    
    def _get_default_features(self) -> Dict[str, float]:
        """Return default feature set for error cases"""
        return {
            'hour_of_day': 12.0,
            'day_of_week': 1.0,
            'is_weekend': 0.0,
            'transaction_amount': 0.0,
            'device_fingerprint_entropy': 0.0,
            'txn_count_24h': 0.0,
            'behavior_pattern_deviation': 0.5,
            'geo_new_location': 0.0,
            'account_age_days': 1.0,
            'network_device_shared': 0.0,
            'risk_avg_trust_score': 70.0
        }
